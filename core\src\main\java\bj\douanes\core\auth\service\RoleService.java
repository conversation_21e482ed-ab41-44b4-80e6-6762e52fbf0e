package bj.douanes.core.auth.service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import bj.douanes.core.auth.model.Role;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.repository.RoleRepository;
import bj.douanes.core.auth.repository.UserRepo;
import bj.douanes.core.shared.error.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface RoleService {
    
    /**
     * Récupère tous les rôles
     */
    List<Role> findAll();
    
    /**
     * Trouve un rôle par son nom
     */
    Optional<Role> findByName(String name);
    
    /**
     * Crée un nouveau rôle
     */
    Role createRole(String name, String description);
    
    /**
     * Met à jour un rôle existant
     */
    Role updateRole(String name, String newDescription);
    
    /**
     * Supprime un rôle
     */
    void deleteRole(String name);
    
    /**
     * Assigne un rôle à un utilisateur
     */
    void assignRoleToUser(UUID userId, String roleName);

    /**
     * Retire un rôle d'un utilisateur
     */
    void removeRoleFromUser(UUID userId, String roleName);

    /**
     * Récupère tous les rôles d'un utilisateur
     */
    Set<Role> getUserRoles(UUID userId);

    /**
     * Vérifie si un utilisateur a un rôle spécifique
     */
    boolean userHasRole(UUID userId, String roleName);
    
    /**
     * Crée les rôles par défaut s'ils n'existent pas
     */
    void initializeDefaultRoles();
}

@Slf4j
@Service
@RequiredArgsConstructor
class RoleServiceImpl implements RoleService {
    
    private final RoleRepository roleRepository;
    private final UserRepo userRepository;
    
    @Override
    public List<Role> findAll() {
        log.debug("Récupération de tous les rôles");
        return roleRepository.findAll();
    }
    
    @Override
    public Optional<Role> findByName(String name) {
        log.debug("Recherche du rôle: {}", name);
        return roleRepository.findByName(name);
    }
    
    @Override
    @Transactional
    public Role createRole(String name, String description) {
        log.info("Création du rôle: {} - {}", name, description);
        
        if (roleRepository.existsByName(name)) {
            throw new ApiException("Le rôle '" + name + "' existe déjà");
        }
        
        Role role = Role.builder()
                .name(name.toUpperCase())
                .description(description)
                .build();
        
        return roleRepository.save(role);
    }
    
    @Override
    @Transactional
    public Role updateRole(String name, String newDescription) {
        log.info("Mise à jour du rôle: {}", name);
        
        Role role = roleRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Rôle '" + name + "' non trouvé"));
        
        role.setDescription(newDescription);
        return roleRepository.save(role);
    }
    
    @Override
    @Transactional
    public void deleteRole(String name) {
        log.info("Suppression du rôle: {}", name);
        
        Role role = roleRepository.findByName(name)
                .orElseThrow(() -> new ApiException("Rôle '" + name + "' non trouvé"));
        
        // Vérifier si le rôle est utilisé par des utilisateurs
        Long userCount = roleRepository.countUsersByRoleName(name);
        if (userCount > 0) {
            throw new ApiException("Impossible de supprimer le rôle '" + name + 
                    "'. Il est utilisé par " + userCount + " utilisateur(s)");
        }
        
        roleRepository.delete(role);
    }
    
    @Override
    @Transactional
    public void assignRoleToUser(UUID userId, String roleName) {
        log.info("Attribution du rôle '{}' à l'utilisateur {}", roleName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));
        
        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new ApiException("Rôle '" + roleName + "' non trouvé"));
        
        if (user.hasRole(roleName)) {
            log.warn("L'utilisateur {} a déjà le rôle '{}'", userId, roleName);
            return;
        }
        
        user.addRole(role);
        userRepository.save(user);
    }
    
    @Override
    @Transactional
    public void removeRoleFromUser(UUID userId, String roleName) {
        log.info("Retrait du rôle '{}' de l'utilisateur {}", roleName, userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));
        
        Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new ApiException("Rôle '" + roleName + "' non trouvé"));
        
        if (!user.hasRole(roleName)) {
            log.warn("L'utilisateur {} n'a pas le rôle '{}'", userId, roleName);
            return;
        }
        
        user.removeRole(role);
        userRepository.save(user);
    }
    
    @Override
    public Set<Role> getUserRoles(UUID userId) {
        log.debug("Récupération des rôles de l'utilisateur {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));
        
        return user.getRoles();
    }
    
    @Override
    public boolean userHasRole(UUID userId, String roleName) {
        log.debug("Vérification si l'utilisateur {} a le rôle '{}'", userId, roleName);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ApiException("Utilisateur non trouvé avec l'ID: " + userId));
        
        return user.hasRole(roleName);
    }
    
    @Override
    @Transactional
    public void initializeDefaultRoles() {
        log.info("Initialisation des rôles par défaut");
        
        // Créer le rôle de base s'ils n'existent pas
        createRoleIfNotExists("ADMIN", "Administrateur avec tous les privilèges");
        createRoleIfNotExists("USER", "Utilisateur standard avec accès de base");
    }
    
    private void createRoleIfNotExists(String name, String description) {
        if (!roleRepository.existsByName(name)) {
            Role role = Role.builder()
                    .name(name)
                    .description(description)
                    .build();
            roleRepository.save(role);
            log.info("Rôle '{}' créé avec succès", name);
        } else {
            log.debug("Le rôle '{}' existe déjà", name);
        }
    }
}
