# Guide de Gestion d'Exceptions Avancée

## 🎯 Vue d'ensemble

La classe `RestExceptionHandler` a été considérablement enrichie pour offrir une gestion d'erreurs robuste et professionnelle dans l'application ERP Douanes.

## 🔧 Fonctionnalités Principales

### ✅ **Gestion Complète des Exceptions**

1. **Exceptions de Validation** - Erreurs de données d'entrée
2. **Exceptions Métier** - Erreurs logiques de l'application  
3. **Exceptions de Base de Données** - Erreurs de persistance
4. **Exceptions de Sécurité** - Erreurs d'authentification/autorisation
5. **Exceptions HTTP** - Erreurs de protocole et format
6. **Exceptions Générales** - Toutes autres erreurs

### ✅ **Fonctionnalités Avancées**

- **ID de traçage unique** pour chaque erreur
- **Logs contextuels** avec informations détaillées
- **Réponses adaptées** selon l'environnement (dev/prod)
- **Headers HTTP** informatifs
- **Messages d'erreur localisés** en français

## 📋 Types d'Exceptions Gérées

### 1. **Exceptions de Validation**

```java
// Validation des arguments de méthode
@ExceptionHandler(MethodArgumentNotValidException.class)
// Violations de contraintes
@ExceptionHandler(ConstraintViolationException.class)
```

**Exemple de réponse :**
```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "400 BAD_REQUEST",
  "error": "Erreurs de validation des données",
  "message": "Erreurs de validation des données",
  "fieldErrors": [
    {
      "message": "Le nom est obligatoire",
      "fieldName": "nom",
      "rejectedValue": null,
      "constraintName": "NotBlank"
    }
  ]
}
```

### 2. **Exceptions de Base de Données**

```java
// Violations d'intégrité
@ExceptionHandler(DataIntegrityViolationException.class)
// Entités non trouvées
@ExceptionHandler(EntityNotFoundException.class)
// Conflits de verrouillage optimiste
@ExceptionHandler(OptimisticLockException.class)
```

### 3. **Exceptions de Sécurité**

```java
// Erreurs d'authentification
@ExceptionHandler({BadCredentialsException.class, AuthenticationException.class})
// Erreurs d'autorisation
@ExceptionHandler(AccessDeniedException.class)
// Erreurs JWT
@ExceptionHandler({ExpiredJwtException.class, MalformedJwtException.class, SignatureException.class})
```

### 4. **Exceptions HTTP**

```java
// Méthodes non supportées
@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
// Types de média non supportés
@ExceptionHandler(HttpMediaTypeNotSupportedException.class)
// Paramètres manquants
@ExceptionHandler(MissingServletRequestParameterException.class)
// Erreurs de format JSON
@ExceptionHandler(HttpMessageNotReadableException.class)
// Fichiers trop volumineux
@ExceptionHandler(MaxUploadSizeExceededException.class)
// Pages non trouvées
@ExceptionHandler(NoHandlerFoundException.class)
```

## 🔍 Traçage et Logging

### **ID de Traçage Unique**

Chaque erreur génère un ID unique de 8 caractères :

```
ERROR_ID: a1b2c3d4 | STATUS: 400 | MESSAGE: Validation failed | PATH: /api/agents
```

### **Headers de Réponse**

```http
X-Error-ID: a1b2c3d4
Allow: GET, POST (pour les erreurs 405)
```

### **Logs Contextuels**

```
ERROR_ID: a1b2c3d4 | STATUS: 500 | MESSAGE: Database error | PATH: /api/agents | 
USER_AGENT: Mozilla/5.0... | IP: ************* | TIMESTAMP: 2024-01-15 10:30:00
```

## ⚙️ Configuration

### **Propriétés Disponibles**

```properties
# Inclure la stack trace (dev uniquement)
app.error.include-stacktrace=false

# Inclure les erreurs de validation détaillées
app.error.include-binding-errors=true

# Profil actif (détermine le niveau de détail)
spring.profiles.active=dev
```

### **Configuration par Environnement**

#### **Développement**
- Messages d'erreur détaillés
- Stack traces incluses
- Logs DEBUG activés

#### **Production**
- Messages génériques pour la sécurité
- Pas de stack traces
- Logs ERROR uniquement

## 🎛️ Utilisation

### **Lancer une Exception Métier**

```java
@Service
public class AgentService {
    
    public Agent findById(Long id) {
        return agentRepository.findById(id)
            .orElseThrow(() -> new ApiException(
                "Agent non trouvé avec l'ID: " + id, 
                HttpStatus.NOT_FOUND
            ));
    }
    
    public Agent create(Agent agent) {
        if (agentRepository.existsByMatricule(agent.getMatricule())) {
            throw new ApiException(
                "Un agent avec ce matricule existe déjà", 
                HttpStatus.CONFLICT
            );
        }
        return agentRepository.save(agent);
    }
}
```

### **Validation avec Bean Validation**

```java
@Entity
public class Agent {
    
    @NotBlank(message = "Le nom est obligatoire")
    @Size(max = 100, message = "Le nom ne peut dépasser 100 caractères")
    private String nom;
    
    @Email(message = "Format d'email invalide")
    private String email;
    
    @Pattern(regexp = "\\d{6}", message = "Le matricule doit contenir 6 chiffres")
    private String matricule;
}
```

## 📊 Exemples de Réponses

### **Erreur de Validation (400)**

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "400 BAD_REQUEST",
  "error": "Erreurs de validation des données",
  "fieldErrors": [
    {
      "message": "Le nom est obligatoire",
      "fieldName": "nom",
      "rejectedValue": "",
      "constraintName": "NotBlank"
    }
  ]
}
```

### **Erreur d'Authentification (401)**

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "401 UNAUTHORIZED",
  "error": "Identifiants invalides",
  "message": "Identifiants invalides"
}
```

### **Erreur d'Autorisation (403)**

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "403 FORBIDDEN",
  "error": "Accès refusé : permissions insuffisantes",
  "message": "Accès refusé : permissions insuffisantes"
}
```

### **Ressource Non Trouvée (404)**

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "404 NOT_FOUND",
  "error": "Ressource non trouvée",
  "message": "Ressource non trouvée"
}
```

### **Erreur Interne (500) - Production**

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "status": "500 INTERNAL_SERVER_ERROR",
  "error": "Une erreur interne s'est produite",
  "message": "Contactez l'administrateur avec l'ID: a1b2c3d4"
}
```

## 🚀 Avantages

✅ **Cohérence** : Toutes les erreurs suivent le même format  
✅ **Traçabilité** : ID unique pour chaque erreur  
✅ **Sécurité** : Messages adaptés selon l'environnement  
✅ **Debugging** : Logs détaillés avec contexte  
✅ **UX** : Messages d'erreur clairs en français  
✅ **Monitoring** : Facilite la surveillance des erreurs  

La gestion d'erreurs est maintenant robuste et prête pour la production ! 🎉
