package bj.douanes.facade;

import java.util.List;
import java.util.UUID;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import bj.douanes.core.auth.dto.AuthReqDto;
import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.service.AuthService;
import bj.douanes.core.auth.service.CrudService;
import bj.douanes.core.shared.error.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

public interface AuthFacade {
    public AuthReqDto signUp(Object signupReq);

    public AuthReqDto signIn(Object signinReq);

    List<User> getAllUsers();

    User getUserById(UUID id);

    User saveUser(Object user);

    User updateUser(UUID id, Object user);

    String deleteUser(UUID id);
}

@Slf4j
@Service
@RequiredArgsConstructor
class AuthFacadeImpl implements AuthFacade {

    private final ModelMapper modelMapper;
    private final AuthService authService;
    private final CrudService crudService;

    @Override
    public AuthReqDto signUp(Object signupReq) {
        final AuthReqDto authReqRes = this.modelMapper.map(signupReq, AuthReqDto.class);
        log.info("BEGIN SIGNUP");
        return authService.signUp(authReqRes);
    }

    @Override
    public AuthReqDto signIn(Object signinReq) {
        final AuthReqDto authReqRes = this.modelMapper.map(signinReq, AuthReqDto.class);
        log.info("BEGIN SIGNIN");
        return authService.signIn(authReqRes);
    }

    @Override
    public List<User> getAllUsers() {
        log.info("BEGIN getAllUsers");
        return crudService.findAll();
    }

    @Override
    public User getUserById(UUID id) {
        log.info("BEGIN getUserById");
        return crudService.findById(id).orElseThrow(
            ()-> new ApiException("User with ID: "+ id +" not exist")
        );
    }

    @Override
    public User saveUser(Object user) {
        log.info("BEGIN saveUser");
        User u = modelMapper.map(user, User.class);
        return crudService.save(u);
    }

    @Override
    public User updateUser(UUID id, Object user) {
        log.info("BEGIN updateUser");
        User u = crudService.findById(id).orElseThrow(ApiException::new);
        modelMapper.map(user, u);
        return crudService.save(u);
    }

    @Override
    public String deleteUser(UUID id) {
        log.info("BEGIN deleteUser");
        crudService.delete(id);
        return "true";
    }

}
