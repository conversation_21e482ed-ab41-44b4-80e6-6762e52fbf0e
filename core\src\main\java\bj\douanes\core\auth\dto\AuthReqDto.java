package bj.douanes.core.auth.dto;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthReqDto {

    private String token;
    private Long id;
    private String email;
    private String roles;
    private String password;

    private String matricule;
    private Boolean isSuspended;
    private LocalDate expirationDate;

}
