package bj.douanes.core.auth.repository;

import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import bj.douanes.core.auth.model.User;

@Repository
public interface UserRepo extends JpaRepository<User, UUID> {
    // @Query("SELECT u FROM User u WHERE u.email = :email")
    public Optional<User> findByEmailIgnoreCase(String email);
}
