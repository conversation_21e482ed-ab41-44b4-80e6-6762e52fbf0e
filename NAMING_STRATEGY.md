# Configuration de Nomenclature : camelCase → snake_case

## 🎯 Configuration Actuelle

Le projet ERP Douanes est maintenant configuré avec la **stratégie de naming Hibernate standard** par défaut :

### ✅ **Conversion Automatique : `camelCase` → `snake_case`**

```java
@Entity
public class Agent {
    private String nomComplet;        // → nom_complet
    private LocalDate dateNaissance;  // → date_naissance
    private String numTelephone;      // → num_telephone
    private String adresseEmail;      // → adresse_email
}
```

## 🔧 Comment ça fonctionne

### 1. **Configuration Globale**

Dans `DataSourceConfig.java`, la stratégie est appliquée automatiquement :

```java
// Configuration de la stratégie de naming par défaut : camelCase → snake_case
properties.put("hibernate.physical_naming_strategy", 
    "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
```

### 2. **Exemples de Conversion**

| Champ Java (camelCase) | Colonne <PERSON> (snake_case) |
|------------------------|-------------------------|
| `id`                   | `id`                    |
| `nom`                  | `nom`                   |
| `nomComplet`           | `nom_complet`           |
| `dateNaissance`        | `date_naissance`        |
| `numeroTelephone`      | `numero_telephone`      |
| `adresseEmailPro`      | `adresse_email_pro`     |
| `dateCreationCompte`   | `date_creation_compte`  |

### 3. **Noms de Tables**

```java
@Entity
public class AgentPersonnel {    // → agent_personnel
    // ...
}

@Entity  
public class MutationNomination { // → mutation_nomination
    // ...
}
```

## 🎛️ Contrôle Explicite (Priorité Absolue)

Les annotations JPA **remplacent** la stratégie automatique :

```java
@Entity
@Table(name = "Personnel")  // Nom explicite : Personnel
public class Agent {
    
    @Column(name = "date_nais")     // Nom explicite : date_nais
    private LocalDate dateNaissance;
    
    @Column(name = "nom_complet")   // Nom explicite : nom_complet  
    private String nomComplet;
    
    private String telephone;       // Automatique : telephone
    private String adresseEmail;    // Automatique : adresse_email
}
```

## 📋 Exemples Pratiques

### **Nouvelle Entité (Recommandé)**

```java
@Entity
public class Departement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;                    // → id
    
    private String nomDepartement;      // → nom_departement
    private String codeDepartement;     // → code_departement
    private LocalDate dateCreation;    // → date_creation
    private String responsableEmail;   // → responsable_email
    private Boolean estActif;          // → est_actif
}
```

### **Entité avec Table Existante**

```java
@Entity
@Table(name = "Personnel")  // Table existante
public class Agent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // Colonnes existantes avec noms explicites
    @Column(name = "date_nais")
    private LocalDate dateNaissance;
    
    @Column(name = "nom")
    private String nomComplet;
    
    // Nouvelles colonnes utilisent la conversion automatique
    private String numeroSecuriteSociale;  // → numero_securite_sociale
    private LocalDate dateEmbauche;        // → date_embauche
}
```

### **Relations avec Conversion Automatique**

```java
@Entity
public class Mutation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;                        // → id
    
    private LocalDate dateDebutMutation;   // → date_debut_mutation
    private LocalDate dateFinMutation;     // → date_fin_mutation
    private String ancienPoste;            // → ancien_poste
    private String nouveauPoste;           // → nouveau_poste
    
    @ManyToOne
    @JoinColumn(name = "agent_id")  // Nom explicite pour la FK
    private Agent agent;
}
```

## 🔄 Migration des Entités Existantes

### **Option 1 : Garder les Noms Existants**

```java
@Entity
@Table(name = "convoyage")  // Table existante
public class Fiche {
    @Column(name = "serialNumber")      // Garder le nom existant
    private String serialNumber;
    
    @Column(name = "cuoCode")           // Garder le nom existant
    private String cuoCode;
    
    // Nouveaux champs utilisent la conversion
    private String numeroConvoi;        // → numero_convoi
    private LocalDate dateConvoyage;    // → date_convoyage
}
```

### **Option 2 : Migrer vers snake_case**

```java
@Entity
@Table(name = "convoyage")
public class Fiche {
    // Migration progressive : renommer les colonnes en DB
    @Column(name = "serial_number")     // Nouvelle colonne snake_case
    private String serialNumber;
    
    @Column(name = "cuo_code")          // Nouvelle colonne snake_case
    private String cuoCode;
    
    // Nouveaux champs automatiques
    private String numeroConvoi;        // → numero_convoi
    private LocalDate dateConvoyage;    // → date_convoyage
}
```

## ✅ Avantages de cette Configuration

🎯 **Cohérence** : Toutes les nouvelles entités suivent la même convention  
🔄 **Automatique** : Pas besoin d'annotations pour les nouveaux champs  
📚 **Standard** : Suit les conventions Java/Spring Boot  
🛡️ **Flexible** : Contrôle explicite quand nécessaire  
🔧 **Maintenable** : Code plus propre et lisible  

## 🚀 Prochaines Étapes

1. **Testez** avec une nouvelle entité simple
2. **Vérifiez** que les colonnes sont créées correctement
3. **Migrez progressivement** les entités existantes si souhaité
4. **Documentez** les exceptions dans votre équipe

La configuration est maintenant active et s'appliquera automatiquement à toutes vos entités ! 🎉
