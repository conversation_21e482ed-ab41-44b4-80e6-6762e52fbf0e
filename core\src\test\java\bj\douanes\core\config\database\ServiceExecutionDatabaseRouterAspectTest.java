package bj.douanes.core.config.database;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.aspectj.lang.ProceedingJoinPoint;

import static org.mockito.Mockito.*;

/**
 * Test unitaire pour ServiceExecutionDatabaseRouterAspect
 */
@ExtendWith(MockitoExtension.class)
public class ServiceExecutionDatabaseRouterAspectTest {

    @Mock
    private DataSourceType dataSourceType;
    
    @Mock
    private TransactionalHelper transactionalHelper;
    
    @Mock
    private PackageDataSourceMapping packageMapping;
    
    @Mock
    private ProceedingJoinPoint proceedingJoinPoint;
    
    @InjectMocks
    private ServiceExecutionDatabaseRouterAspect aspect;

    @Test
    public void testRoutingForPersonalService() throws Throwable {
        // Arrange
        Object mockTarget = new Object() {
            @Override
            public String toString() {
                return "bj.douanes.personal.service.PersonalService";
            }
        };
        
        when(proceedingJoinPoint.getTarget()).thenReturn(mockTarget);
        when(packageMapping.getDataSourceForClass("bj.douanes.personal.service.PersonalService"))
            .thenReturn("primary");
        when(dataSourceType.setName("primary")).thenReturn("primary");
        when(dataSourceType.first).thenReturn("primary");
        when(transactionalHelper.runWithTransaction(any())).thenReturn("result");

        // Act
        Object result = aspect.routeDataSource(proceedingJoinPoint);

        // Assert
        verify(packageMapping).getDataSourceForClass("bj.douanes.personal.service.PersonalService");
        verify(dataSourceType).setName("primary");
        verify(transactionalHelper).runWithTransaction(any());
    }

    @Test
    public void testRoutingForTransportService() throws Throwable {
        // Arrange
        Object mockTarget = new Object() {
            @Override
            public String toString() {
                return "bj.douanes.transport.service.TransportService";
            }
        };
        
        when(proceedingJoinPoint.getTarget()).thenReturn(mockTarget);
        when(packageMapping.getDataSourceForClass("bj.douanes.transport.service.TransportService"))
            .thenReturn("transport_db");
        when(dataSourceType.setName("transport_db")).thenReturn("transport_db");
        when(transactionalHelper.runWithTransaction(any())).thenReturn("result");

        // Act
        Object result = aspect.routeDataSource(proceedingJoinPoint);

        // Assert
        verify(packageMapping).getDataSourceForClass("bj.douanes.transport.service.TransportService");
        verify(dataSourceType).setName("transport_db");
        verify(transactionalHelper).runWithTransaction(any());
    }
}
