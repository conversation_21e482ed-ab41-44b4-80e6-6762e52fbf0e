package bj.douanes.facade.UTILS;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


public class DTO {
    @Data
    public static final class UserReq {
        @NotBlank(message = "You must inter an email.")
        @Email
        private String email;
        private String roles;
        private String password;
        
        private String matricule;
        // private String lastName;
        // private String firstName;
        // private String phone;
        // private String sex;
        // private LocalDate birthday;
        // private String lang;
        // private String city;
        // private String address;
        // private String link;
        // private Boolean validEmail;
        // private Boolean validPhone;
        // private LocalDate expirationDate;
    }

    @Data
    public static final class AgentDto {

        private String email;
        private String matricule;
        private String nom;
        private String prenom;
        private String sexe;
        private LocalDate dateNais;
        private LocalDate dateMariage;
        private String situationFam;
        private String lieuNais;
        private int enfantCharge;
        private int nbrEnfant;
        private String addr1;
        private String addr2;
        private String bp;
        private String cell;
        private String observation;
        private String categorie;
        private byte[] profil; 
        
        private SituationAdmDto situationAd;
        private SituationFncDto SituationFnc;
        private List<ActDto> actage;
        private List<CertificationDto> diplomes;
        private List<MutationDto> mutation;
        private List<PrimeDto> prime;
        private List<NotationDto> notations;
        private List<ChildDto> agentChildrens;
        private List<ConjointDto> agentConjoints;
        private List<LangueDto> langues;
        private List<SanctionDto> santions;
        private List<DebPositionDto> debPosition;
        private List<FinPositionDto> finPosition;

    }

    @Data
    public static final class MutationNomDto{
        private String matricule;
        private LocalDate datDebut;
        private LocalDate datFin;
        private String service;
        private String direction;
        private String fonction;
        private String codeTitre;
    }

    @Data
    public static final class SituationAdmDto{

        private String matricule;
        private LocalDate datePriseServFp;
        private LocalDate datePriseServAdm;
        private Boolean nonActive;
        private String positionActuel;
        private String codeCollect;
        private String statusAgent;
        private String tel;
        private String structure;
        private String sousStructure;
        private String servBurAss;
        private String regimePension;
        private String profAvantEntrerService;
        private LocalDate dateNomEmploi;
        private String caisse;
    
    } 

    @Data
    public static final class SituationFncDto{

        private String comSuivEscort;
        private String codeCorps;
        private String gradePaye;
        private double coefPaye;
        private String gradeReel;
        private double coefReel;
        private String fonction;
        private LocalDate dateEffet;
        private String primeForfait;
        private String numeroSecuSocial;
        private String pointPaye;
        private String rib;
    }

    @Data
    public static final class CertificationDto{

        private String matricule;
        private String certifYear;
        private String codeCertif;
        private String libelleCodeCertif;
        private String codeEcole;
        private String libelleCodeEcole;
    }

    @Data
    public static final class PrimeDto{

        private String yearPrime;
        private String orthersCode;
        private BigDecimal montantComiteSuiv;
        private BigDecimal montantCagnoteClo;
        private BigDecimal montantCagnoteTs;
        private BigDecimal montantBonification;
        private LocalDate dateDebut;
        private LocalDate dateFin;
        private Long nbrJrs;
        private String titreEtat;
        private String codeType;
        private Boolean clopinetteTs;
        private Boolean archiver;
        private Boolean tenirCompteAncienP;
    }

    @Data
    public static final class ActDto{
        private String actNumber;
        private String actType;
        private String actYear;
        private String actNumber2;
        private String agentMatricule;
        private LocalDate acteDate;
        private LocalDate acteDateEffet;
        private String codeDate;
    }

    @Data
    public static final class NotationDto{

        private String matricule;
        private String yearNotation;
        private String codeRub;
        private double note;
    }

    @Data
    public static final class ChildDto {
        private String name;
        private String fistName;
        private LocalDate dateNaiss;
        private String lieuNaiss;
        private String acteNaiss;
        private String codeSexe;
        private String aCharge;
        private LocalDate dateDecesEnfant;
        
    }

    @Data
    public static final class ConjointDto{
        private String nom;
        private String prenom;
        private String nomJeuneFille;
        private LocalDate dateNaiss;
        private String typeLiaison;
        private String refactMariage;
        private LocalDate decesConjoint;
        private String matricule;

    }

    @Data
    public static final class MutationDto{
        private String matricule;
        private LocalDate datDebut;
        private LocalDate datFin;
        private String service;
        private String direction;
        private String fonction;
        private String codeTitre;
    }

    @Data
    public static final class LangueDto{
        private String codeLangue;
        private String libLangue;
    }

    @Data
    public static final class SanctionDto{
        private String codeSanction;
        private String libSanction;
    }

    @Data
    public static final class DebPositionDto{
        private String matriculeAgent;
        private String codePosition;
        private String libellePosition;
        private String refActPosition;
        private LocalDate dateActPosition;
        private LocalDate dateDemarage;
        private String pays;
    }

    @Data
    public static final class FinPositionDto{
        private String refActFinPos;
        private LocalDate dateActFinPos;
        private String refCorrespReprise;
        private LocalDate dateCorrespReprise;
        private LocalDate dateReprise;
    }

    //DTO for Prime
    @Data
    public static final class TypePrimeDto{
        private String code_type_prime;
        private String libelle;
        private String description;
        private String periodicite;
        private String nom_table_prime;
        private Boolean avecUnite;
        private LocalDate created_at;
        private LocalDate updated_at;
    }

    @Data
    public static final class RepartitionDto{
        private String codeTypePrime;
        // private Long id_prime;
        private String periode;
        private String annee;
        private LocalDate createdAt;
        private LocalDate updatedAt;
    }

    // @Data
    // public class PrimeTSDUniteDto {
    // private Integer idPrimeUnite;
    // private BigDecimal montantVerset;
    // private BigDecimal oeuvreSocialUnite;
    // private BigDecimal bonificationUnite;
    // private BigDecimal partUnite;
    // private BigDecimal cumulCoef;
    // private LocalDateTime createdAt;
    // private LocalDateTime updatedAt;
    // private String codeUnite;
    // private Long idRepartition;
    // }
   
}
