package bj.douanes.web;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.modelmapper.ModelMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import bj.douanes.core.auth.dto.RoleCreateDto;
import bj.douanes.core.auth.dto.RoleDto;
import bj.douanes.core.auth.dto.RoleUpdateDto;
import bj.douanes.core.auth.dto.UserRoleAssignmentDto;
import bj.douanes.core.auth.model.Role;
import bj.douanes.core.auth.service.RoleService;
import bj.douanes.core.shared.response.AppResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("api/admin/roles")
@SecurityRequirement(name = "lockApi")
@Tag(name = "Gestion des Rôles", description = "API pour la gestion des rôles et permissions")
@PreAuthorize("hasAuthority('ADMIN')")
public class RoleController {

    private final RoleService roleService;
    private final ModelMapper modelMapper;

    @GetMapping
    @Operation(summary = "Récupérer tous les rôles", description = "Récupère la liste de tous les rôles disponibles")
    public ResponseEntity<?> getAllRoles() {
        log.info("Récupération de tous les rôles");
        
        List<Role> roles = roleService.findAll();
        List<RoleDto> roleDtos = roles.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        return AppResponse.ok(roleDtos);
    }

    @GetMapping("/{name}")
    @Operation(summary = "Récupérer un rôle par nom", description = "Récupère un rôle spécifique par son nom")
    public ResponseEntity<?> getRoleByName(@PathVariable String name) {
        log.info("Récupération du rôle: {}", name);
        
        Role role = roleService.findByName(name)
                .orElseThrow(() -> new RuntimeException("Rôle non trouvé: " + name));
        
        return AppResponse.ok(convertToDto(role));
    }

    @PostMapping
    @Operation(summary = "Créer un nouveau rôle", description = "Crée un nouveau rôle avec nom et description")
    public ResponseEntity<?> createRole(@Valid @RequestBody RoleCreateDto roleCreateDto) {
        log.info("Création d'un nouveau rôle: {}", roleCreateDto.getName());
        
        Role createdRole = roleService.createRole(
                roleCreateDto.getName(), 
                roleCreateDto.getDescription()
        );
        
        return AppResponse.created(convertToDto(createdRole));
    }

    @PutMapping("/{name}")
    @Operation(summary = "Mettre à jour un rôle", description = "Met à jour la description d'un rôle existant")
    public ResponseEntity<?> updateRole(
            @PathVariable String name, 
            @Valid @RequestBody RoleUpdateDto roleUpdateDto) {
        
        log.info("Mise à jour du rôle: {}", name);
        
        Role updatedRole = roleService.updateRole(name, roleUpdateDto.getDescription());
        
        return AppResponse.ok(convertToDto(updatedRole));
    }

    @DeleteMapping("/{name}")
    @Operation(summary = "Supprimer un rôle", description = "Supprime un rôle s'il n'est utilisé par aucun utilisateur")
    public ResponseEntity<?> deleteRole(@PathVariable String name) {
        log.info("Suppression du rôle: {}", name);
        
        roleService.deleteRole(name);
        
        return AppResponse.ok("Rôle '" + name + "' supprimé avec succès");
    }

    @PostMapping("/assign")
    @Operation(summary = "Assigner des rôles à un utilisateur", description = "Assigne un ou plusieurs rôles à un utilisateur")
    public ResponseEntity<?> assignRolesToUser(@Valid @RequestBody UserRoleAssignmentDto assignmentDto) {
        log.info("Attribution des rôles {} à l'utilisateur {}", 
                assignmentDto.getRoleNames(), assignmentDto.getUserId());
        
        for (String roleName : assignmentDto.getRoleNames()) {
            roleService.assignRoleToUser(assignmentDto.getUserId(), roleName);
        }
        
        return AppResponse.ok("Rôles assignés avec succès");
    }

    @DeleteMapping("/remove/{userId}/{roleName}")
    @Operation(summary = "Retirer un rôle d'un utilisateur", description = "Retire un rôle spécifique d'un utilisateur")
    public ResponseEntity<?> removeRoleFromUser(
            @PathVariable UUID userId,
            @PathVariable String roleName) {
        
        log.info("Retrait du rôle '{}' de l'utilisateur {}", roleName, userId);
        
        roleService.removeRoleFromUser(userId, roleName);
        
        return AppResponse.ok("Rôle retiré avec succès");
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "Récupérer les rôles d'un utilisateur", description = "Récupère tous les rôles d'un utilisateur spécifique")
    public ResponseEntity<?> getUserRoles(@PathVariable UUID userId) {
        log.info("Récupération des rôles de l'utilisateur {}", userId);
        
        var userRoles = roleService.getUserRoles(userId);
        List<RoleDto> roleDtos = userRoles.stream()
                .map(this::convertToDto)
                .toList();
        
        return AppResponse.ok(roleDtos);
    }

    @PostMapping("/initialize")
    @Operation(summary = "Initialiser les rôles par défaut", description = "Crée les rôles par défaut s'ils n'existent pas")
    public ResponseEntity<?> initializeDefaultRoles() {
        log.info("Initialisation des rôles par défaut");
        
        roleService.initializeDefaultRoles();
        
        return AppResponse.ok("Rôles par défaut initialisés avec succès");
    }

    private RoleDto convertToDto(Role role) {
        RoleDto dto = modelMapper.map(role, RoleDto.class);
        // Ajouter le nombre d'utilisateurs si nécessaire
        // dto.setUserCount(role.getUsers().size());
        return dto;
    }
}
