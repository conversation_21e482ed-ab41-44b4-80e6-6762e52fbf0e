package bj.douanes.core.auth.service;

import bj.douanes.core.auth.model.User;
import bj.douanes.core.auth.repository.UserRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CrudService {
    public List<User> findAll();

    public Optional<User> findById(UUID id);

    public User findByEmail(String email);

    public User save(User user);

    public void delete(UUID id);
}

@Service
@RequiredArgsConstructor
class ImplCrudService implements CrudService {

    private final UserRepo repository;

    @Override
    public List<User> findAll() {
        return repository.findAll();
    }

    @Override
    public Optional<User> findById(UUID id) {
        return repository.findById(id);
    }

    @Override
    public User findByEmail(String email) {
        return repository.findByEmailIgnoreCase(email).orElseThrow();
    }

    @Override
    @Transactional
    public User save(User user) {
        return repository.save(user);
    }

    @Override
    @Transactional
    public void delete(UUID id) {
        repository.deleteById(id);
    }
}