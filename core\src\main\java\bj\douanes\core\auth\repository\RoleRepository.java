package bj.douanes.core.auth.repository;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import bj.douanes.core.auth.model.Role;

@Repository
public interface RoleRepository extends JpaRepository<Role, String> {
    
    /**
     * Trouve un rôle par son nom
     */
    Optional<Role> findByName(String name);
    
    /**
     * Vérifie si un rôle existe par son nom
     */
    boolean existsByName(String name);
    
    /**
     * Trouve tous les rôles dont le nom contient la chaîne spécifiée (insensible à la casse)
     */
    @Query("SELECT r FROM Role r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    Set<Role> findByNameContainingIgnoreCase(@Param("name") String name);
    
    /**
     * Trouve tous les rôles par une liste de noms
     */
    @Query("SELECT r FROM Role r WHERE r.name IN :names")
    Set<Role> findByNameIn(@Param("names") Set<String> names);
    
    /**
     * Compte le nombre d'utilisateurs ayant un rôle spécifique
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.name = :roleName")
    Long countUsersByRoleName(@Param("roleName") String roleName);
}
