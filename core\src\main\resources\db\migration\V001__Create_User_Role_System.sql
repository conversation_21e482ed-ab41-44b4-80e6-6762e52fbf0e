-- Migration pour créer le nouveau système d'utilisateurs et de rôles
-- Version: V001
-- Description: Création des tables utilisateurs, roles et user_roles, Insertion des données existantes

-- 0. Activer l'extension UUID si elle n'est pas déjà activée
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

DROP TABLE IF EXISTS utilisateurs;

-- 1. <PERSON><PERSON>er la table utilisateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    matricule VARCHAR(255),
    is_suspended BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON> la table roles
CREATE TABLE IF NOT EXISTS roles (
    name VARCHA<PERSON>(50) PRIMARY KEY,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Créer la table de liaison user_roles
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID NOT NULL,
    role_name VARCHAR(50) NOT NULL,
    PRIMARY KEY (user_id, role_name),
    FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (role_name) REFERENCES roles(name) ON DELETE CASCADE
);

-- 4. Insérer les rôles par défaut
INSERT INTO roles (name, description) VALUES 
    ('ADMIN', 'Administrateur avec tous les privilèges'),
    ('USER', 'Utilisateur standard avec accès de base')
ON CONFLICT (name) DO NOTHING;

-- Fin
