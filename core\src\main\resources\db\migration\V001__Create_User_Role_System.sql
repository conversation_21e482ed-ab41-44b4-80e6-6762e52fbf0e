-- Migration pour créer le nouveau système de rôles
-- Version: V001
-- Description: Création des tables roles et user_roles, migration des données existantes

-- 1. <PERSON><PERSON><PERSON> la table roles
CREATE TABLE IF NOT EXISTS roles (
    name VARCHAR(50) PRIMARY KEY,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. <PERSON><PERSON><PERSON> la table de liaison user_roles
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_name VARCHAR(50) NOT NULL,
    PRIMARY KEY (user_id, role_name),
    FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (role_name) REFERENCES roles(name) ON DELETE CASCADE
);

-- 3. Insérer les rôles par défaut
INSERT INTO roles (name, description) VALUES 
    ('USER', 'Utilisateur standard avec accès de base'),
    ('ADMIN', 'Administrateur avec tous les privilèges'),
    ('MANAGER', 'Gestionnaire avec privilèges étendus'),
    ('VIEWER', 'Utilisateur en lecture seule')
ON CONFLICT (name) DO NOTHING;

-- 4. Migrer les données existantes de la colonne roles vers la nouvelle table
-- Cette partie dépend de la structure actuelle des données
-- Supposons que les rôles sont stockés comme "ROLE1,ROLE2,ROLE3"

-- Créer une fonction temporaire pour parser les rôles (PostgreSQL)
-- Si vous utilisez MySQL, adaptez la syntaxe

-- Pour PostgreSQL:
DO $$
DECLARE
    user_record RECORD;
    role_name TEXT;
    roles_array TEXT[];
BEGIN
    -- Parcourir tous les utilisateurs ayant des rôles
    FOR user_record IN 
        SELECT id, roles 
        FROM utilisateurs 
        WHERE roles IS NOT NULL AND roles != ''
    LOOP
        -- Diviser la chaîne de rôles par les virgules
        roles_array := string_to_array(user_record.roles, ',');
        
        -- Pour chaque rôle
        FOREACH role_name IN ARRAY roles_array
        LOOP
            role_name := TRIM(UPPER(role_name));
            
            -- Créer le rôle s'il n'existe pas
            INSERT INTO roles (name, description) 
            VALUES (role_name, 'Rôle migré automatiquement')
            ON CONFLICT (name) DO NOTHING;
            
            -- Assigner le rôle à l'utilisateur
            INSERT INTO user_roles (user_id, role_name)
            VALUES (user_record.id, role_name)
            ON CONFLICT (user_id, role_name) DO NOTHING;
        END LOOP;
    END LOOP;
    
    -- Assigner le rôle USER par défaut aux utilisateurs sans rôles
    INSERT INTO user_roles (user_id, role_name)
    SELECT id, 'USER'
    FROM utilisateurs 
    WHERE (roles IS NULL OR roles = '')
    AND id NOT IN (SELECT user_id FROM user_roles)
    ON CONFLICT (user_id, role_name) DO NOTHING;
END $$;

-- 5. Optionnel: Supprimer l'ancienne colonne roles après vérification
-- ATTENTION: Décommentez seulement après avoir vérifié que la migration s'est bien passée
-- ALTER TABLE utilisateurs DROP COLUMN IF EXISTS roles;

-- 6. Créer des index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_name ON user_roles(role_name);
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);

-- 7. Ajouter des contraintes de validation
ALTER TABLE roles ADD CONSTRAINT chk_role_name_not_empty CHECK (LENGTH(TRIM(name)) > 0);

-- 8. Mettre à jour les timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Fin de la migration
